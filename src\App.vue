<script setup lang="ts">
import { ref } from "vue";
import StyleForm from "./components/StyleForm.vue";
import MapComponents from "./components/mapComponents.vue";
import TdMap from "./components/tdMap.vue";

// 当前活跃的标签页
const activeTab = ref("tdMap");

// 样式数据状态
const styleData = ref({
  fillColor: "#409EFF",
  borderColor: "#000000",
  borderWidth: 2,
  lineStyle: "solid",
  radius: 50000,
});

// 处理样式表单数据更新
const handleStyleUpdate = (newStyleData: any) => {
  styleData.value = { ...newStyleData };
};

// 标签页数据
const tabs = [
  {
    name: "tdMap",
    label: "天地图",
  },
  {
    name: "buffer",
    label: "缓冲区示例",
  },
];
</script>

<template>
  <div class="app-container">
    <!-- 标签页导航 -->
    <div class="tab-navigation">
      <div class="tab-buttons">
        <el-tabs v-model="activeTab" type="card">
          <el-tab-pane
            v-for="item in tabs"
            :key="item.name"
            :label="item.label"
            :name="item.name"
          >
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>

    <!-- 标签页内容 -->
    <div class="tab-content">
      <!-- ArcGIS Demo -->
      <div v-if="activeTab === 'tdMap'" class="tab-pane arcgis-pane">
        <TdMap />
      </div>
      <div v-if="activeTab === 'buffer'" class="tab-pane arcgis-pane">
        <div class="content-layout">
          <!-- 左侧样式配置面板 -->
          <div class="sidebar">
            <h2>样式配置</h2>
            <StyleForm
              :initial-data="styleData"
              @update-style="handleStyleUpdate"
            />
          </div>

          <!-- 右侧地图显示区域 -->
          <div class="map-container">
            <h2>地图显示</h2>
            <MapComponents :data="styleData" />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* 标签页导航 */
.tab-navigation {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
}

.tab-header {
  text-align: center;
  margin-bottom: 30px;
}

.tab-header h1 {
  font-size: 2.5rem;
  margin-bottom: 10px;
  background: linear-gradient(45deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.tab-header p {
  color: #666;
  font-size: 1.1rem;
  margin: 0;
}

.tab-buttons {
  width: 100%;
}

.tab-button {
  background: white;
  border: 2px solid #e0e0e0;
  border-radius: 15px;
  padding: 20px 30px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 250px;
  text-align: left;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

.tab-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
  border-color: #667eea;
}

.tab-button.active {
  background: linear-gradient(45deg, #667eea, #764ba2);
  border-color: transparent;
  color: white;
}

.tab-label {
  display: block;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 5px;
}

.tab-description {
  display: block;
  font-size: 0.9rem;
  opacity: 0.8;
}

.tab-button.active .tab-description {
  opacity: 0.9;
}

/* 标签页内容 */
.tab-content {
  height: calc(100vh - 150px);
  overflow: hidden;
}

.tab-pane {
  height: 100%;
  width: 100%;
}

/* ArcGIS 面板样式 */
.arcgis-pane {
  padding: 20px;
  background: #f5f5f5;
}

.content-layout {
  display: flex;
  gap: 20px;
  height: 100%;
}

.sidebar {
  width: 350px;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  overflow-y: auto;
}

.sidebar h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

.map-container {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.map-container h2 {
  margin-top: 0;
  margin-bottom: 20px;
  color: #333;
  font-size: 18px;
  border-bottom: 2px solid #409eff;
  padding-bottom: 10px;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .tab-buttons {
    flex-direction: column;
    align-items: center;
  }

  .tab-button {
    min-width: 300px;
  }

  .content-layout {
    flex-direction: column;
    height: auto;
  }

  .sidebar {
    width: 100%;
    margin-bottom: 20px;
  }

  .map-container {
    height: 500px;
  }
}

@media (max-width: 768px) {
  .tab-header h1 {
    font-size: 2rem;
  }

  .tab-button {
    min-width: 250px;
  }
}
</style>
