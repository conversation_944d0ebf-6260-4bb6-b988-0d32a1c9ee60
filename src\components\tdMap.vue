<template>
  <div class="td-container" id="tdMap"></div>
  <div style="position: absolute; bottom: 20px; right: 20px">
    <el-button @click="changeLayer">切换图层</el-button>
    <el-button @click="addMarker">随机添加标记</el-button>
    <el-button @click="getAllMarker">获取所有标记</el-button>
  </div>
</template>

<script setup lang="ts">
import { onMounted, shallowRef } from "vue";
import Map from "@arcgis/core/Map";
import MapView from "@arcgis/core/views/MapView";
import WebTileLayer from "@arcgis/core/layers/WebTileLayer";
import Point from "@arcgis/core/geometry/Point";
import Graphic from "@arcgis/core/Graphic";
import SimpleMarkerSymbol from "@arcgis/core/symbols/SimpleMarkerSymbol";
import GraphicsLayer from "@arcgis/core/layers/GraphicsLayer";
import PopTemplate from "@arcgis/core/PopupTemplate";

// 天地图密钥
const tdToken = "d52454825ecd067fadfb6677494fd387";
// 天地图底图
const tdBottomLayer = new WebTileLayer({
  title: "天地图底图",
  id: "tdBottomlayer",
  urlTemplate: `http://t0.tianditu.gov.cn/vec_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=vec&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${tdToken}`,
});

// 天地图标记图层
const tdVectorLayer = new WebTileLayer({
  title: "天地图标记图层",
  id: "tdVectorLayer",
  urlTemplate: `http://t0.tianditu.gov.cn/cva_w/wmts?SERVICE=WMTS&REQUEST=GetTile&VERSION=1.0.0&LAYER=cva&STYLE=default&TILEMATRIXSET=w&FORMAT=tiles&TILEMATRIX={level}&TILEROW={row}&TILECOL={col}&tk=${tdToken}`,
});
const mapView = shallowRef();
const myMap = shallowRef();
const init = () => {
  const map = new Map({
    layers: [tdBottomLayer, tdVectorLayer],
  });

  myMap.value = map;

  const view = new MapView({
    container: "tdMap",
    map: map,
    center: [116.4, 39.9],
    zoom: 3,
    constraints: {
      minZoom: 3,
      maxZoom: 18,
    },
  });
  mapView.value = view;
};

onMounted(() => {
  init();
  clickMarker();
});

const changeLayer = () => {
  if (mapView.value) {
    tdVectorLayer.visible = !tdVectorLayer.visible;
  }
};

// 随机添加标记
const markersLayer = shallowRef();
const addMarker = () => {
  // 添加标记前先把之前的弹窗关掉
  mapView.value.popup.close();

  if (!markersLayer.value) {
    markersLayer.value = new GraphicsLayer({
      id: "markersLayer",
      title: "管理标记图层",
    });
    myMap.value.add(markersLayer.value);
  }
  if (mapView.value) {
    const point = new Point({
      longitude: Math.random() * 180 - 90,
      latitude: Math.random() * 90 - 45,
      spatialReference: {
        wkid: 4326,
      },
    });
    const marker = new Graphic({
      geometry: point,
      symbol: new SimpleMarkerSymbol({
        color: "red",
        size: "20px",
        outline: {
          color: "white",
          width: 1,
        },
      }),
      // 获取经纬度
      attributes: {
        longitude: point.longitude,
        latitude: point.latitude,
        name: "标记",
        description: "随机添加的标记",
      },
    });
    markersLayer.value.add(marker);
    // mapView.value.graphics.add(marker);
    mapView.value.when(function () {
      mapView.value.goTo({
        target: point,
        zoom: 3,
      });
    });
  }
};

// 获取所有标记
const getAllMarker = () => {
  if (mapView.value) {
    const allLayers = myMap.value.layers
      .find((layer: any) => layer.id === "markersLayer")
      .graphics.toArray();
    alert(allLayers.length);
  }
};
const popupTemplate = new PopTemplate({
  title: "标记信息",
  content: `
    <div>
      <p><b>描述:</b> {description}</p>
      <p><b>经度:</b> {longitude}</p>
      <p><b>纬度:</b> {latitude}</p>
    </div>
  `,
});

// 点击标记
const clickMarker = () => {
  // 获取当前点击的这个标记
  if (mapView.value) {
    mapView.value.on("click", (event: any) => {
      console.log(mapView.value.zoom, "mapZoom");

      mapView.value.hitTest(event).then((item: any) => {
        console.log(item);
        if (item.results.length > 0) {
          const graphic = item.results[0].graphic;
          console.log(graphic, "当前点击的这个标记");
          // 点击打开弹窗
          graphic.popupTemplate = popupTemplate;

          mapView.value.popup.open({
            features: [graphic],
            location: graphic.geometry,
          });
        }
      });
    });
  }
};
</script>

<style scoped>
.td-container {
  width: 100%;
  height: 100%;
}
</style>
